import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Level } from '../types/jlpt';
import { colors, spacing, fontSize, borderRadius, globalStyles } from '../styles/theme';
import { levels } from '../data/jlptData';

interface LevelAccessBarProps {
  onLevelPress: (level: Level) => void;
}

export const LevelAccessBar: React.FC<LevelAccessBarProps> = ({
  onLevelPress
}) => {
  return (
    <View style={[styles.container, globalStyles.card]}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="book-outline" size={20} color={colors.slate[600]} />
          <Text style={styles.title}>查看完整文章</Text>
        </View>
      </View>
      
      <View style={styles.levelButtons}>
        {levels.map((level) => (
          <TouchableOpacity
            key={level}
            style={[
              styles.levelButton,
              { backgroundColor: colors.level[level] }
            ]}
            onPress={() => onLevelPress(level)}
            activeOpacity={0.8}
          >
            <Text style={styles.levelButtonText}>{level}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: spacing.lg
  },

  header: {
    marginBottom: spacing.md
  },

  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },

  title: {
    fontSize: fontSize.base,
    fontWeight: '600',
    color: colors.slate[700],
    marginLeft: spacing.sm
  },

  levelButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
    flexWrap: 'wrap'
  },

  levelButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    minWidth: 60,
    alignItems: 'center'
  },

  levelButtonText: {
    color: colors.white,
    fontSize: fontSize.sm,
    fontWeight: '600'
  }
});