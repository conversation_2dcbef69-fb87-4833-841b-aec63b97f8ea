import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { colors, fontSize, globalStyles, spacing } from '../styles/theme';
import { NavigationControlsProps } from '../types/jlpt';

export const NavigationControls: React.FC<NavigationControlsProps> = ({
  currentIndex,
  totalCount,
  onPrevious,
  onNext,
  canGoBack,
  canGoForward
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          globalStyles.button,
          canGoBack ? styles.enabledButton : styles.disabledButton
        ]}
        onPress={onPrevious}
        disabled={!canGoBack}
        activeOpacity={0.7}
        hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
      >
        <Ionicons
          name="chevron-back"
          size={20}
          color={canGoBack ? colors.slate[700] : colors.slate[400]}
        />
        <Text style={[
          styles.buttonText,
          canGoBack ? styles.enabledText : styles.disabledText
        ]}>
          前へ
        </Text>
      </TouchableOpacity>

      <View style={styles.counter}>
        <Text style={styles.currentNumber}>
          {currentIndex + 1} / {totalCount}
        </Text>
        <View style={styles.progressContainer}>
          <View style={styles.progressBackground}>
            <View
              style={[
                styles.progressFill,
                { width: `${((currentIndex + 1) / totalCount) * 100}%` }
              ]}
            />
          </View>
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.button,
          globalStyles.button,
          canGoForward ? styles.primaryButton : styles.disabledButton
        ]}
        onPress={onNext}
        disabled={!canGoForward}
        activeOpacity={0.7}
        hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
      >
        <Text style={[
          styles.buttonText,
          canGoForward ? styles.primaryText : styles.disabledText
        ]}>
          次へ
        </Text>
        <Ionicons
          name="chevron-forward"
          size={20}
          color={canGoForward ? colors.white : colors.slate[400]}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.lg,
    marginVertical: spacing.lg,
    paddingHorizontal: spacing.md
  },

  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.sm,
    minWidth: 100,
    minHeight: 44, // iOS minimum touch target
  },

  enabledButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.slate[200]
  },

  primaryButton: {
    backgroundColor: colors.primary
  },

  disabledButton: {
    backgroundColor: colors.slate[200]
  },

  buttonText: {
    fontSize: fontSize.base,
    fontWeight: '600'
  },

  enabledText: {
    color: colors.slate[700]
  },

  primaryText: {
    color: colors.white
  },

  disabledText: {
    color: colors.slate[400]
  },

  counter: {
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    minWidth: 80,
  },

  currentNumber: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    color: colors.slate[800],
    marginBottom: spacing.xs,
  },

  progressContainer: {
    width: 60,
    alignItems: 'center',
  },

  progressBackground: {
    width: '100%',
    height: 4,
    backgroundColor: colors.slate[200],
    borderRadius: 2,
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  }
});
