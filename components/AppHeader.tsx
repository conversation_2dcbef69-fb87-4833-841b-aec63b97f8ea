import { Ionicons } from '@expo/vector-icons';
import React, { useState, useEffect } from 'react';
import {
  Dimensions,
  FlatList,
  Modal,
  Pressable,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { colors, spacing, typography } from '../styles/theme';
import { Article } from '../types/article';
import { Level } from '../types/jlpt';

interface AppHeaderProps {
  currentArticle: Article | null;
  onMenuPress: () => void;
  onLevelSelect: (level: Level) => void;
  availableLevels: Level[];
  loading?: boolean;
}

const levelLabels: Record<Level, string> = {
  'N5': 'N5 入门',
  'N4': 'N4 初级',
  'N3': 'N3 中级',
  'N2': 'N2 中高级',
  'N1': 'N1 高级',
};

const categoryLabels: Record<string, string> = {
  'daily-life': '日常生活',
  'school-life': '学校生活',
  'travel': '旅行',
};

const difficultyLabels: Record<string, string> = {
  'beginner': '初級',
  'intermediate': '中級',
  'advanced': '上級',
};

export const AppHeader: React.FC<AppHeaderProps> = ({
  currentArticle,
  onMenuPress,
  onLevelSelect,
  availableLevels,
  loading = false,
}) => {
  const [showLevelSelector, setShowLevelSelector] = useState(false);
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
  
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenWidth(window.width);
    });

    return () => subscription?.remove();
  }, []);

  // iPad landscape threshold (1024px is typical iPad landscape width)
  const shouldShowLevelSelector = screenWidth >= 1024;

  const handleLevelPress = () => {
    setShowLevelSelector(true);
  };

  const handleLevelSelect = (level: Level) => {
    setShowLevelSelector(false);
    onLevelSelect(level);
  };

  const renderLevelItem = ({ item }: { item: Level }) => (
    <Pressable
      style={styles.levelItem}
      onPress={() => handleLevelSelect(item)}
    >
      <Text style={styles.levelItemText}>{levelLabels[item]}</Text>
      <Text style={styles.levelItemDescription}>
        {currentArticle?.jlptData[item]?.description || ''}
      </Text>
    </Pressable>
  );

  return (
    <>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          {/* Menu Button */}
          <TouchableOpacity
            style={styles.menuButton}
            onPress={onMenuPress}
            disabled={loading}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name="menu"
              size={24}
              color={loading ? colors.slate[400] : colors.slate[700]}
            />
          </TouchableOpacity>

          {/* Article Title */}
          <View style={styles.titleContainer}>
            <Text style={styles.title} numberOfLines={1}>
              {loading ? '読み込み中...' : currentArticle?.title || '記事を選択'}
            </Text>
            {currentArticle && (
              <Text style={styles.subtitle} numberOfLines={1}>
                {categoryLabels[currentArticle.category] || currentArticle.category} • {difficultyLabels[currentArticle.difficulty] || currentArticle.difficulty}
              </Text>
            )}
          </View>

          {/* Level Selector - Only show on wide screens (iPad landscape and up) */}
          {shouldShowLevelSelector && (
            <TouchableOpacity
              style={styles.levelSelector}
              onPress={handleLevelPress}
              disabled={loading || !currentArticle}
              activeOpacity={0.7}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Text style={[
                styles.levelSelectorText,
                (loading || !currentArticle) && styles.disabledText
              ]}>
                レベル
              </Text>
              <Ionicons
                name="chevron-down"
                size={16}
                color={loading || !currentArticle ? colors.slate[400] : colors.slate[600]}
              />
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>

      {/* Level Selection Modal */}
      <Modal
        visible={showLevelSelector}
        transparent
        animationType="fade"
        onRequestClose={() => setShowLevelSelector(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowLevelSelector(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>レベル選択</Text>
              <TouchableOpacity
                onPress={() => setShowLevelSelector(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={colors.slate[600]} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={availableLevels}
              renderItem={renderLevelItem}
              keyExtractor={(item) => item}
              style={styles.levelList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </Pressable>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.slate[200],
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    minHeight: 60,
  },

  menuButton: {
    padding: spacing.sm,
    marginRight: spacing.md,
    minHeight: 44, // iOS minimum touch target
    minWidth: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },

  titleContainer: {
    flex: 1,
    marginRight: spacing.md,
  },

  title: {
    ...typography.h3,
    color: colors.slate[900],
    marginBottom: 2,
  },

  subtitle: {
    ...typography.caption,
    color: colors.slate[600],
  },

  levelSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.slate[50],
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.slate[200],
    minHeight: 44, // iOS minimum touch target
    justifyContent: 'center',
  },

  levelSelectorText: {
    ...typography.body2,
    color: colors.slate[700],
    marginRight: spacing.xs,
  },

  disabledText: {
    color: colors.slate[400],
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    margin: spacing.lg,
    maxHeight: '85%',
    minWidth: 320,
    width: '90%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.slate[200],
  },

  modalTitle: {
    ...typography.h3,
    color: colors.slate[900],
  },

  closeButton: {
    padding: spacing.xs,
  },

  levelList: {
    flexGrow: 0,
    flexShrink: 0,
  },

  levelItem: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.slate[100],
  },

  levelItemText: {
    ...typography.body1,
    color: colors.slate[900],
    marginBottom: spacing.xs,
    fontWeight: '600',
  },

  levelItemDescription: {
    ...typography.body2,
    color: colors.slate[600],
    lineHeight: 18,
  },
});
