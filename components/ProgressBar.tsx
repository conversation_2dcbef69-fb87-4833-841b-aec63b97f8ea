import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ProgressBarProps } from '../types/jlpt';
import { colors, spacing, borderRadius, globalStyles } from '../styles/theme';

export const ProgressBar: React.FC<ProgressBarProps> = ({
  current,
  total,
  style
}) => {
  const percentage = total > 0 ? (current / total) * 100 : 0;

  return (
    <View style={[styles.container, globalStyles.card, style]}>
      <View style={styles.header}>
        <Text style={[globalStyles.body, styles.label]}>学习进度</Text>
        <Text style={[globalStyles.caption, styles.counter]}>
          {current} / {total} 句
        </Text>
      </View>
      
      <View style={styles.progressTrack}>
        <View 
          style={[
            styles.progressFill,
            { width: `${percentage}%` }
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.lg
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm
  },
  
  label: {
    fontWeight: '600',
    color: colors.slate[600]
  },
  
  counter: {
    color: colors.slate[500]
  },
  
  progressTrack: {
    width: '100%',
    height: 8,
    backgroundColor: colors.slate[200],
    borderRadius: borderRadius.full,
    overflow: 'hidden'
  },
  
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: borderRadius.full,
    minWidth: 4 // 确保即使是0%也有一点显示
  }
});