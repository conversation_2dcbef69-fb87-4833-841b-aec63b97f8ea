import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LevelCardProps } from '../types/jlpt';
import { colors, spacing, fontSize, borderRadius, globalStyles } from '../styles/theme';

export const LevelCard: React.FC<LevelCardProps> = ({
  level,
  sentence,
  grammarFeature,
  isActive = false,
  onPress
}) => {
  const levelColor = colors.level[level];
  const bgColor = colors.background[level];

  const cardStyle = [
    styles.container,
    { backgroundColor: bgColor },
    isActive && styles.active
  ];

  const borderStyle = [
    styles.border,
    { borderLeftColor: levelColor }
  ];

  return (
    <TouchableOpacity 
      style={cardStyle}
      onPress={onPress}
      activeOpacity={0.7}
      hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
    >
      <View style={borderStyle}>
        <View style={styles.header}>
          <View style={[styles.levelBadge, { backgroundColor: levelColor }]}>
            <Text style={styles.levelText}>{level}</Text>
          </View>
          
          <View style={styles.grammarTag}>
            <Text style={styles.grammarText}>{grammarFeature}</Text>
          </View>
        </View>
        
        <Text style={[styles.sentence, globalStyles.japaneseText]}>
          {sentence}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: borderRadius.xl,
    padding: spacing.lg,
    marginBottom: spacing.md,
    ...globalStyles.card
  },
  
  active: {
    transform: [{ scale: 1.02 }],
    elevation: 8,
    shadowOpacity: 0.2
  },
  
  border: {
    borderLeftWidth: 4,
    paddingLeft: spacing.md
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.sm
  },
  
  levelBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
    minWidth: 60,
    alignItems: 'center'
  },
  
  levelText: {
    color: colors.white,
    fontSize: fontSize.sm,
    fontWeight: '700'
  },
  
  grammarTag: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
    flex: 1
  },
  
  grammarText: {
    color: colors.slate[700],
    fontSize: fontSize.xs,
    textAlign: 'center'
  },
  
  sentence: {
    fontSize: fontSize.lg,
    lineHeight: 28,
    color: colors.slate[800]
  }
});