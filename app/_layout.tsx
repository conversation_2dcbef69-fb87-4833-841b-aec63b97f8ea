import { Stack } from "expo-router";
import Head from "expo-router/head";
import { Platform } from "react-native";
import { GestureHandlerRootView } from 'react-native-gesture-handler';

export default function RootLayout() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      {Platform.OS === 'web' && (
        <Head>
          <title>読解 - 日本語学習アプリ</title>
          <meta name="description" content="JLPT対応の日本語学習アプリ。N5からN1まで段階的に同じ内容を比較学習できます。" />
          <meta name="keywords" content="日本語学習,JLPT,N5,N4,N3,N2,N1,日本語能力試験,学習アプリ,Japanese learning" />
          <meta name="author" content="DOKKAI" />
          <meta property="og:title" content="読解 - 日本語学習アプリ" />
          <meta property="og:description" content="JLPT対応の日本語学習アプリ。N5からN1まで段階的に同じ内容を比較学習できます。" />
          <meta property="og:type" content="website" />
          <meta property="og:locale" content="ja_JP" />
          <meta name="twitter:card" content="summary" />
          <meta name="twitter:title" content="読解 - 日本語学習アプリ" />
          <meta name="twitter:description" content="JLPT对应的日本語学習アプリ。N5からN1まで段階的に同じ内容を比較学習できます。" />
        </Head>
      )}
      <Stack
        screenOptions={{
          headerShown: false, // We'll use our custom header
          contentStyle: {
            backgroundColor: "#f8f9fa",
          },
        }}
      >
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
          }}
        />
      </Stack>
    </GestureHandlerRootView>
  );
}
