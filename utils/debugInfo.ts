/**
 * 调试信息工具
 * 用于在开发过程中显示当前的配置信息
 */
import Constants from 'expo-constants';

export const getApiDebugInfo = () => {
  const API_BASE_URL = Constants.expoConfig?.extra?.API_BASE_URL || 'http://localhost:3001/api';
  const healthCheckUrl = API_BASE_URL.replace('/api', '/health');

  return {
    apiBaseUrl: API_BASE_URL,
    healthCheckUrl,
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
  };
};

export const logApiInfo = () => {
  const info = getApiDebugInfo();
  const DATA_SOURCE_MODE = Constants.expoConfig?.extra?.DATA_SOURCE_MODE || 'local';
  const useApiService = DATA_SOURCE_MODE === 'api' && info.apiBaseUrl.trim() !== '';

  console.log('🔧 Data Source Configuration:');
  console.log(`   Mode: ${DATA_SOURCE_MODE}`);
  console.log(`   API Base URL: ${info.apiBaseUrl}`);
  console.log(`   Health Check: ${info.healthCheckUrl}`);
  console.log(`   Environment: ${info.environment}`);
  console.log(`   Using API Service: ${useApiService}`);
};

export const testApiConnection = async () => {
  const info = getApiDebugInfo();

  try {
    console.log(`🔍 Testing API connection to: ${info.healthCheckUrl}`);
    const response = await fetch(info.healthCheckUrl);
    const data = await response.json();

    if (data.status === 'healthy') {
      console.log('✅ API server is healthy');
      return true;
    } else {
      console.log('❌ API server returned unhealthy status');
      return false;
    }
  } catch (error) {
    console.log(`💥 Failed to connect to API server: ${error}`);
    console.log('💡 Make sure the API server is running with: npm run api:dev');
    return false;
  }
};
