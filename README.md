# Dokkai - JLPT 日语学习应用

Dokkai 专注于通过不同 JLPT 级别的对比来帮助学习者理解日语表达的层次和复杂性。

## 功能特点

### 🎯 核心功能
- **多级别对比学习**: 同一内容的 N5-N1 级别表达对比
- **文章切换**: 支持多篇文章，涵盖不同主题和场景
- **进度跟踪**: 自动保存学习进度，支持断点续学
- **语法解析**: 每个级别都有对应的语法特点说明

### 📱 用户界面
- **简洁的头部导航**: 文章标题、菜单按钮、级别选择器
- **卡片式布局**: 清晰展示不同级别的表达方式
- **底部导航**: 上一句/下一句快速切换
- **模态框**: 完整文章查看和级别选择

### 📚 内容管理
- **文章系统**: 结构化的文章数据管理
- **标签分类**: 支持按主题、难度、类别筛选
- **元数据**: 阅读时间估算、句子数量等信息

## 技术架构

### 前端框架
- **React Native**: 跨平台移动应用开发
- **Expo**: 开发工具链和部署平台

### 项目结构
```
dokkai/
├── app/                    # 路由和页面
│   ├── _layout.tsx        # 根布局 (Stack 导航)
│   └── index.tsx          # 主页面
├── components/            # 可复用组件
│   ├── AppHeader.tsx      # 应用头部
│   ├── ArticleSelector.tsx # 文章选择器
│   ├── JLPTLearning.tsx   # 主学习组件
│   ├── LevelCard.tsx      # 级别卡片
│   ├── NavigationControls.tsx # 导航控制
│   └── ...
├── data/                  # 数据文件
│   ├── index.json         # 文章索引
│   └── articles/          # 文章数据
│       ├── daily-life-student.json
│       ├── school-activities.json
│       └── travel-adventure.json
├── hooks/                 # 自定义 Hooks
│   └── useArticleData.ts  # 文章数据管理
├── services/              # 业务逻辑服务
│   └── articleService.ts  # 文章服务
├── types/                 # TypeScript 类型定义
│   ├── article.ts         # 文章相关类型
│   └── jlpt.ts           # JLPT 相关类型
├── styles/               # 样式和主题
│   └── theme.ts          # 主题配置
└── utils/                # 工具函数
    └── dataUtils.ts      # 数据处理工具
```

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npx expo start
```

### 3. 运行应用
在输出中选择合适的平台：
- **Android 模拟器**: 按 `a`
- **iOS 模拟器**: 按 `i`
- **Web 浏览器**: 按 `w`
- **Expo Go**: 扫描二维码

## 数据结构

### 文章格式
每篇文章包含完整的 JLPT 数据结构：

```typescript
interface Article {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  estimatedReadingTime: number;
  jlptData: {
    N5: LevelData;
    N4: LevelData;
    N3: LevelData;
    N2: LevelData;
    N1: LevelData;
  };
}
```

### 级别数据
每个级别包含：
- `sentences`: 句子数组
- `grammar_features`: 语法特点说明
- `full_text`: 完整文本
- `grammar_points`: 语法要点
- `word_count`: 字数统计

## 开发指南

### 添加新文章
1. 在 `data/articles/` 目录创建新的 JSON 文件
2. 更新 `data/index.json` 添加文章元数据
3. 在 `services/articleService.ts` 中添加文件加载逻辑

### 自定义主题
修改 `styles/theme.ts` 文件来调整：
- 颜色方案
- 字体大小
- 间距设置
- 阴影效果

### 扩展功能
- 在 `hooks/` 目录添加新的状态管理逻辑
- 在 `components/` 目录创建新的 UI 组件
- 在 `services/` 目录添加新的业务逻辑

## 测试

运行测试套件：
```bash
npm test
```

手动测试数据服务：
```typescript
import { runManualTests } from './__tests__/articleService.test';
runManualTests();
```

## 架构变更说明

### 主要改进
1. **去除 Tab 导航**: 改为 Stack 导航，界面更简洁
2. **新增 Header 组件**: 集成文章标题、菜单、级别选择
3. **文章管理系统**: 支持多文章切换和管理
4. **底部导航**: 上一句/下一句操作更便捷
5. **数据结构优化**: 更好的类型安全和错误处理

### 新增组件
- `AppHeader`: 应用头部导航
- `ArticleSelector`: 文章选择模态框
- `useArticleData`: 文章数据管理 Hook
- `articleService`: 文章数据服务

这个重构保持了所有原有功能，同时提供了更好的用户体验和代码组织结构。
