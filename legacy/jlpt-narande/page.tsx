'use client';

import { BookOpen, ChevronLeft, ChevronRight, Target, X } from 'lucide-react';
import React, { useState } from 'react';

// 定义类型
export type Level = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

interface LevelData {
  sentences: string[];
  grammar_features: string[];
  full_text: string;
  grammar_points: string[];
  word_count: number;
  color: string;
  bgColor: string;
  description: string;
}

const levelData: Record<Level, LevelData> = {
  "N5": {
    "sentences": [
      "はじめまして。私の名前は田中です。二十歳です。大学生です。",
      "毎朝七時に起きます。朝ごはんを食べます。大学に行きます。",
      "授業は九時に始まります。",
      "昼休みは友達と学食で昼ごはんを食べます。美味しいです。",
      "午後は図書館で勉強します。宿題があります。大変です。",
      "夕方、家に帰ります。晩ごはんの後、テレビを見ます。",
      "週末は映画を見ます。買い物をします。新しい服を買います。",
      "来月、友達と京都に行きます。楽しみです。",
      "日本語の勉強は難しいです。頑張ります。",
      "ありがとうございました。"
    ],
    "grammar_features": [
      "基本自己介绍", "日常作息", "时间表达", "共同行动+评价", "学习活动+状态", "归家+晚间活动",
      "周末活动", "未来计划+心情", "学习感想+决心", "感谢表达"
    ],
    "full_text": "はじめまして。私の名前は田中です。二十歳です。大学生です。毎朝七時に起きます。朝ごはんを食べます。大学に行きます。授業は九時に始まります。昼休みは友達と学食で昼ごはんを食べます。美味しいです。午後は図書館で勉強します。宿題があります。大変です。夕方、家に帰ります。晩ごはんの後、テレビを見ます。週末は映画を見ます。買い物をします。新しい服を買います。来月、友達と京都に行きます。楽しみです。日本語の勉強は難しいです。頑張ります。ありがとうございました。",
    "grammar_points": ["基本助词(は/が/を/に/で/と)","基础动词ます形","形容词+です","时间表达","简单句结构"],
    "word_count": 95,
    "color": "bg-green-500",
    "bgColor": "bg-green-50",
    "description": "入门级：简单句式，基础语法"
  },
  "N4": {
    "sentences": [
      "はじめまして。私の名前は田中です。二十歳です。大学生です。",
      "毎朝七時に起きます。朝ごはんを食べてから、大学に行きます。",
      "授業は九時から始まります。",
      "昼休みは友達と一緒に学食で昼ごはんを食べます。とても美味しいです。",
      "午後は図書館で勉強します。宿題がたくさんあります。ちょっと大変です。",
      "夕方、家に帰ります。晩ごはんの後、テレビを見ます。",
      "週末は映画を見たり、買い物をしたりします。新しい服を買うのが好きです。",
      "来月、友達と京都に旅行に行きます。とても楽しみです。",
      "日本語の勉強は難しいですが、頑張ります。",
      "ありがとうございました。"
    ],
    "grammar_features": [
      "基本自己介绍", "て形连接", "から表示起点", "一緒に+程度副词", "量词+程度副词", "归家+晚间活动",
      "たり形并列+の+が好き", "旅行+程度副词", "逆接が", "感谢表达"
    ],
    "full_text": "はじめまして。私の名前は田中です。二十歳です。大学生です。毎朝七時に起きます。朝ごはんを食べてから、大学に行きます。授業は九時から始まります。昼休みは友達と一緒に学食で昼ごはんを食べます。とても美味しいです。午後は図書館で勉強します。宿題がたくさんあります。ちょっと大変です。夕方、家に帰ります。晩ごはんの後、テレビを見ます。週末は映画を見たり、買い物をしたりします。新しい服を買うのが好きです。来月、友達と京都に旅行に行きます。とても楽しみです。日本語の勉強は難しいですが、頑張ります。ありがとうございました。",
    "grammar_points": ["て形连接(てから)","たり形并列","动词修饰(Vのが好き)","逆接条件(ですが)","副词修饰","一緒に"],
    "word_count": 108,
    "color": "bg-blue-500",
    "bgColor": "bg-blue-50",
    "description": "初级：连接句式，基础复合表达"
  },
  "N3": {
    "sentences": [
      "初めてお会いします。私の名前は田中と申します。二十歳で、大学生をしております。",
      "毎朝七時に起床してから、朝食を摂って大学に通学します。",
      "授業は九時から開始されます。",
      "昼休みには友人と一緒に学生食堂で昼食を取ります。非常に美味しいです。",
      "午後は図書館で学習に励みます。課題が多くあり、少し困難です。",
      "夕方になると帰宅します。夕食後、テレビを視聴します。",
      "週末には映画鑑賞や買い物などをします。新しい洋服を購入することを好んでいます。",
      "来月、友人と京都へ旅行に参ります。大変楽しみにしております。",
      "日本語の学習は困難ですが、努力を続けています。",
      "ありがとうございました。"
    ],
    "grammar_features": [
      "謙譲語+ております", "複合動詞", "受身形", "正式表現+非常に", "に励む+複合述語", "になると+漢語動詞",
      "など並列+ことを好む", "謙譲語参る+ております", "継続ている", "感謝表達"
    ],
    "full_text": "初めてお会いします。私の名前は田中と申します。二十歳で、大学生をしております。毎朝七時に起床してから、朝食を摂って大学に通学します。授業は九時から開始されます。昼休みには友人と一緒に学生食堂で昼食を取ります。非常に美味しいです。午後は図書館で学習に励みます。課題が多くあり、少し困難です。夕方になると帰宅します。夕食後、テレビを視聴します。週末には映画鑑賞や買い物などをします。新しい洋服を購入することを好んでいます。来月、友人と京都へ旅行に参ります。大変楽しみにしております。日本語の学習は困難ですが、努力を続けています。ありがとうございました。",
    "grammar_points": ["敬语表达(申します/しております/参ります)","被动形(開始されます)","に励む","ことを好む","続けています","になると"],
    "word_count": 125,
    "color": "bg-purple-500",
    "bgColor": "bg-purple-50",
    "description": "中级：敬语入门，复杂语法结构"
  },
  "N2": {
    "sentences": [
      "初めてお目にかかります。田中と申します。二十歳の大学生でございます。",
      "毎朝七時に起床いたしまして、朝食を済ませてから大学へ向かいます。",
      "授業は九時より開始いたします。",
      "昼休みには友人たちと学生食堂で昼食を共にいたします。大変美味でございます。",
      "午後は図書館において学習に取り組んでおります。課題が山積しており、やや困難を感じております。",
      "夕刻になりましたら帰宅いたします。夕食を終えた後、テレビを拝見いたします。",
      "週末においては映画鑑賞や買い物等に時間を費やしております。新しい服装を購入することに喜びを感じております。",
      "来月、友人と共に京都への旅行を予定しております。心より楽しみにしております。",
      "日本語の習得は困難を伴いますが、継続的に努力を重ねております。",
      "心より感謝申し上げます。"
    ],
    "grammar_features": [
      "尊敬語+でございます", "いたしまして+へ向かう", "より敬語", "を共にする+でございます", "において+を伴う", "ましたら+拝見いたす",
      "においては+ことに感じる", "と共に+心より", "を伴う+を重ねる", "申し上げる"
    ],
    "full_text": "初めてお目にかかります。田中と申します。二十歳の大学生でございます。毎朝七時に起床いたしまして、朝食を済ませてから大学へ向かいます。授業は九時より開始いたします。昼休みには友人たちと学生食堂で昼食を共にいたします。大変美味でございます。午後は図書館において学習に取り組んでおります。課題が山積しており、やや困難を感じております。夕刻になりましたら帰宅いたします。夕食を終えた後、テレビを拝見いたします。週末においては映画鑑賞や買い物等に時間を費やしております。新しい服装を購入することに喜びを感じております。来月、友人と共に京都への旅行を予定しております。心より楽しみにしております。日本語の習得は困難を伴いますが、継続的に努力を重ねております。心より感謝申し上げます。",
    "grammar_points": ["高级敬语(いたします/ございます/申し上げます)","において/に対して","を共にする","困難を伴う","努力を重ねる","予定しております"],
    "word_count": 142,
    "color": "bg-orange-500",
    "bgColor": "bg-orange-50",
    "description": "中高级：高级敬语，正式文体"
  },
  "N1": {
    "sentences": [
      "初めてお目にかかります。田中と申す者でございます。二十歳になる大学生でございます。",
      "毎朝七時に起床し、朝食を摂った後、大学へと足を向けます。",
      "講義は九時より開講されます。",
      "昼の休憩時間には友人らと学食にて昼食を共にし、その味わいに舌鼓を打っております。",
      "午後は図書館に籠もり、学習に専念しております。課題が山のように積まれており、なかなか手強い状況でございます。",
      "日が傾く頃には帰路につきます。夕餉を済ませた後は、テレビに目を通しております。",
      "週末ともなれば、映画を観たり街を練り歩いたりと、思い思いに過ごしております。新調した衣服を身に纏うことに、ささやかな喜びを覚えております。",
      "来る月には友人と連れ立って、古都京都への小旅行を企てております。今から胸を躍らせているところでございます。",
      "日本語の習得には骨が折れますが、地道に励んでいる次第です。",
      "ご清聴いただき、誠にありがとうございました。"
    ],
    "grammar_features": [
      "申す者+になる", "足を向ける", "開講される", "舌鼓を打つ", "に籠もる+手強い", "日が傾く+目を通す",
      "ともなれば+身に纏う", "連れ立って+胸を躍らせる", "骨が折れる", "ご清聴"
    ],
    "full_text": "初めてお目にかかります。田中と申す者でございます。二十歳になる大学生でございます。毎朝七時に起床し、朝食を摂った後、大学へと足を向けます。講義は九時より開講されます。昼の休憩時間には友人らと学食にて昼食を共にし、その味わいに舌鼓を打っております。午後は図書館に籠もり、学習に専念しております。課題が山のように積まれており、なかなか手強い状況でございます。日が傾く頃には帰路につきます。夕餉を済ませた後は、テレビに目を通しております。週末ともなれば、映画を観たり街を練り歩いたりと、思い思いに過ごしております。新調した衣服を身に纏うことに、ささやかな喜びを覚えております。来る月には友人と連れ立って、古都京都への小旅行を企てております。今から胸を躍らせているところでございます。日本語の習得には骨が折れますが、地道に励んでいる次第です。ご清聴いただき、誠にありがとうございました。",
    "grammar_points": ["古典的表現","慣用句(足を向ける/舌鼓を打つ/籠もる/骨が折れる)","文学的修辞","高度な敬語","複雑な修飾構造","ところでございます"],
    "word_count": 158,
    "color": "bg-red-500",
    "bgColor": "bg-red-50",
    "description": "高级：文学表达，复杂修辞"
  }
};

const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];

const JLPTSentenceComparison = () => {
  const [currentSentence, setCurrentSentence] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [modalLevel, setModalLevel] = useState<Level | null>(null);

  const maxSentences = levelData['N5'].sentences.length;

  const nextSentence = () => {
    if (currentSentence < maxSentences - 1) {
      setCurrentSentence(currentSentence + 1);
    }
  };

  const prevSentence = () => {
    if (currentSentence > 0) {
      setCurrentSentence(currentSentence - 1);
    }
  };

  const openModal = (level: Level) => {
    setModalLevel(level);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setModalLevel(null);
  };

  const getBorderColor = (level: Level) => {
    switch (level) {
      case 'N5': return 'border-green-500';
      case 'N4': return 'border-blue-500';
      case 'N3': return 'border-purple-500';
      case 'N2': return 'border-orange-500';
      case 'N1': return 'border-red-500';
      default: return 'border-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-slate-800 mb-4">
            JLPT 句子对比学习
          </h1>
          <p className="text-slate-600 text-lg">
            同一句意，五种表达 - 逐句感受日语的进阶过程
          </p>
        </div>

        {/* Progress Bar */}
        <div className="bg-white rounded-xl p-4 shadow-lg mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-slate-600">学习进度</span>
            <span className="text-sm text-slate-500">
              {currentSentence + 1} / {maxSentences} 句
            </span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentSentence + 1) / maxSentences) * 100}%` }}
            />
          </div>
        </div>

        {/* Sentence Comparison */}
        <div className="space-y-4 mb-8">
          {levels.map((level) => {
            const sentence = levelData[level].sentences[currentSentence];
            const grammar = levelData[level].grammar_features[currentSentence];

            return (
              <div key={level} className={`${levelData[level].bgColor} rounded-xl p-6 border-l-4 ${getBorderColor(level)} shadow-md`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <span className={`${levelData[level].color} text-white px-3 py-1 rounded-full text-sm font-bold`}>
                        {level}
                      </span>
                      <span className="bg-white/70 text-slate-700 px-2 py-1 rounded text-xs">
                        {grammar}
                      </span>
                    </div>
                    <p className="text-slate-800 text-lg japanese-text leading-relaxed">
                      {sentence}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Navigation Controls */}
        <div className="flex justify-center items-center gap-6 mb-8">
          <button
            onClick={prevSentence}
            disabled={currentSentence === 0}
            className={`flex items-center px-6 py-3 rounded-xl font-medium transition-all ${
              currentSentence === 0
                ? 'bg-slate-200 text-slate-400 cursor-not-allowed'
                : 'bg-white text-slate-700 hover:bg-slate-50 shadow-lg hover:shadow-xl'
            }`}
          >
            <ChevronLeft className="w-5 h-5 mr-2" />
            上一句
          </button>

          <div className="text-center px-6">
            <div className="text-2xl font-bold text-slate-800">
              第 {currentSentence + 1} 句
            </div>
            <div className="text-sm text-slate-500">
              共 {maxSentences} 句
            </div>
          </div>

          <button
            onClick={nextSentence}
            disabled={currentSentence === maxSentences - 1}
            className={`flex items-center px-6 py-3 rounded-xl font-medium transition-all ${
              currentSentence === maxSentences - 1
                ? 'bg-slate-200 text-slate-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 shadow-lg hover:shadow-xl'
            }`}
          >
            下一句
            <ChevronRight className="w-5 h-5 ml-2" />
          </button>
        </div>

        {/* Full Text Access Bar */}
        <div className="bg-white rounded-xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <BookOpen className="w-5 h-5 text-slate-600 mr-3" />
              <span className="font-medium text-slate-700">查看完整文章</span>
            </div>
            <div className="flex gap-2">
              {levels.map((level) => (
                <button
                  key={level}
                  onClick={() => openModal(level)}
                  className={`${levelData[level].color} text-white px-4 py-2 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity`}
                >
                  {level}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Full Text Modal */}
      {showModal && modalLevel && levels.includes(modalLevel) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className={`${levelData[modalLevel].bgColor} p-6 border-b`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className={`${levelData[modalLevel].color} text-white px-4 py-2 rounded-full text-lg font-bold`}>
                    {modalLevel}
                  </span>
                  <div>
                    <h3 className="text-xl font-bold text-slate-800">
                      {modalLevel} 级别完整文章
                    </h3>
                    <p className="text-slate-600 text-sm">
                      {levelData[modalLevel].description}
                    </p>
                  </div>
                </div>
                <button
                  onClick={closeModal}
                  className="text-slate-400 hover:text-slate-600 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="grid lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <div className="flex items-center mb-4">
                    <BookOpen className="w-5 h-5 text-slate-600 mr-2" />
                    <span className="font-medium text-slate-700">完整文章</span>
                    <span className="ml-auto text-sm text-slate-500">
                      {levelData[modalLevel].word_count} 字
                    </span>
                  </div>
                  <div className="bg-slate-50 rounded-lg p-4">
                    <p className="text-slate-800 leading-relaxed japanese-text">
                      {levelData[modalLevel].full_text}
                    </p>
                  </div>
                </div>

                <div>
                  <div className="flex items-center mb-4">
                    <Target className="w-5 h-5 text-slate-600 mr-2" />
                    <span className="font-medium text-slate-700">语法要点</span>
                  </div>
                  <div className="space-y-2">
                    {levelData[modalLevel].grammar_points.map((point: string, index: number) => (
                      <div
                        key={index}
                        className="bg-slate-50 rounded-lg p-3 border border-slate-200"
                      >
                        <span className="text-slate-700 text-sm">{point}</span>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 p-4 bg-slate-100 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-slate-600">统计信息</span>
                    </div>
                    <div className="space-y-1 text-sm text-slate-600">
                      <div>字数：{levelData[modalLevel].word_count}</div>
                      <div>句数：{levelData[modalLevel].sentences.length}</div>
                      <div>语法点：{levelData[modalLevel].grammar_points.length}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .japanese-text {
          font-family: "Hiragino Sans", "ヒラギノ角ゴ ProN W3", "Hiragino Kaku Gothic ProN", "Yu Gothic", "メイリオ", Meiryo, "MS PGothic", sans-serif;
        }
      `}</style>
    </div>
  );
};

export default JLPTSentenceComparison;
