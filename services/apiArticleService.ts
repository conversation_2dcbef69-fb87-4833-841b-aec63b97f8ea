import {
    Article,
    ArticleError,
    ArticleErrorType,
    ArticleIndex,
    ArticleMetadata,
    ArticleProgress,
    ArticleService,
    UserProgress,
} from '../types/article';

// API 配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001/api';
// 可选的其他配置：
// const API_BASE_URL = process.env.API_BASE_URL || 'http://*************:3001/api'; // 局域网访问
// const API_BASE_URL = process.env.API_BASE_URL || 'https://api.dokkai.com/api'; // 生产环境

// API 响应接口
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  details?: any;
}

interface ArticleListResponse {
  articles: ArticleMetadata[];
  total: number;
  version: string;
  lastUpdated: string;
  currentArticle: string;
}

interface SearchResponse {
  articles: ArticleMetadata[];
  total: number;
  query: string;
}

class ApiArticleService implements ArticleService {
  private indexCache: ArticleIndex | null = null;
  private articleCache: Map<string, Article> = new Map();
  private categoriesCache: string[] | null = null;
  private tagsCache: string[] | null = null;

  // HTTP 请求工具函数
  private async fetchApi<T>(endpoint: string): Promise<T> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      console.log(`🌐 API Request: ${url}`);
      console.log(`📊 Mode: API`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: ApiResponse<T> = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'API request failed');
      }

      return result.data!;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw this.createError(
          ArticleErrorType.NETWORK_ERROR,
          'Unable to connect to API server. Please ensure the API server is running on http://localhost:3001'
        );
      }

      throw this.createError(
        ArticleErrorType.NETWORK_ERROR,
        `API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getArticleIndex(): Promise<ArticleIndex> {
    try {
      if (this.indexCache) {
        return this.indexCache;
      }

      const data = await this.fetchApi<ArticleListResponse>('/articles');

      // 构造 ArticleIndex 格式
      const index: ArticleIndex = {
        version: data.version,
        lastUpdated: data.lastUpdated,
        currentArticle: data.currentArticle,
        articles: data.articles,
      };

      this.indexCache = index;
      return index;
    } catch (error) {
      if (error instanceof Error && error.name === 'ArticleError') {
        throw error;
      }
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to load article index: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getArticle(id: string): Promise<Article> {
    try {
      // 检查缓存
      if (this.articleCache.has(id)) {
        return this.articleCache.get(id)!;
      }

      const article = await this.fetchApi<Article>(`/articles/${id}`);

      // 缓存文章
      this.articleCache.set(id, article);
      return article;
    } catch (error) {
      if (error instanceof Error && error.name === 'ArticleError') {
        throw error;
      }
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to load article "${id}": ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getAllArticles(): Promise<Article[]> {
    try {
      const index = await this.getArticleIndex();
      const articles: Article[] = [];

      // 并发加载所有文章
      const articlePromises = index.articles.map(async (metadata) => {
        try {
          return await this.getArticle(metadata.id);
        } catch (error) {
          console.warn(`Failed to load article ${metadata.id}:`, error);
          return null;
        }
      });

      const results = await Promise.all(articlePromises);

      // 过滤掉加载失败的文章
      results.forEach(article => {
        if (article) {
          articles.push(article);
        }
      });

      return articles;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to load all articles: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getFilteredArticles(options: {
    tags?: string[];
    categories?: string[];
    difficulties?: string[];
  } = {}): Promise<ArticleMetadata[]> {
    try {
      // 构建查询参数
      const params = new URLSearchParams();

      if (options.categories && options.categories.length > 0) {
        params.append('category', options.categories[0]); // API 只支持单个分类
      }

      if (options.difficulties && options.difficulties.length > 0) {
        params.append('difficulty', options.difficulties[0]); // API 只支持单个难度
      }

      if (options.tags && options.tags.length > 0) {
        params.append('tags', options.tags.join(',')); // API 支持多个标签
      }

      const queryString = params.toString();
      const endpoint = queryString ? `/articles?${queryString}` : '/articles';

      const data = await this.fetchApi<ArticleListResponse>(endpoint);
      return data.articles;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to filter articles: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getAllTags(): Promise<string[]> {
    try {
      if (this.tagsCache) {
        return this.tagsCache;
      }

      const data = await this.fetchApi<{ tags: string[] }>('/tags');
      this.tagsCache = data.tags;
      return data.tags;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to get all tags: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // 新增方法：获取所有分类
  async getAllCategories(): Promise<string[]> {
    try {
      if (this.categoriesCache) {
        return this.categoriesCache;
      }

      const data = await this.fetchApi<{ categories: string[] }>('/categories');
      this.categoriesCache = data.categories;
      return data.categories;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to get all categories: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // 新增方法：搜索文章
  async searchArticles(query: string, options: {
    category?: string;
    difficulty?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleMetadata[]; total: number }> {
    try {
      const params = new URLSearchParams();
      params.append('q', query);

      if (options.category) {
        params.append('category', options.category);
      }

      if (options.difficulty) {
        params.append('difficulty', options.difficulty);
      }

      if (options.limit) {
        params.append('limit', options.limit.toString());
      }

      if (options.offset) {
        params.append('offset', options.offset.toString());
      }

      const data = await this.fetchApi<SearchResponse>(`/search?${params.toString()}`);
      return {
        articles: data.articles,
        total: data.total,
      };
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to search articles: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // 新增方法：获取特定级别数据
  async getArticleLevelData(articleId: string, level: string): Promise<any> {
    try {
      const data = await this.fetchApi(`/articles/${articleId}/levels/${level}`);
      return data;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to get level data for article "${articleId}" level "${level}": ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // 用户进度相关方法（保持本地存储）
  async updateProgress(progress: ArticleProgress): Promise<void> {
    try {
      // 在实际应用中，这里应该发送到后端 API
      // 目前保持本地存储逻辑
      const userProgress = await this.getUserProgress();
      userProgress.articleProgress[progress.articleId] = progress;
      userProgress.currentArticleId = progress.articleId;

      await this.saveUserProgress(userProgress);
    } catch (error) {
      throw this.createError(
        ArticleErrorType.STORAGE_ERROR,
        `Failed to update progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getUserProgress(): Promise<UserProgress> {
    try {
      // 从本地存储加载用户进度
      // 在实际应用中，这里应该从后端 API 加载
      const defaultProgress: UserProgress = {
        currentArticleId: 'daily-life-student',
        articleProgress: {},
        preferences: {
          preferredLevels: ['N5', 'N4', 'N3', 'N2', 'N1'],
          autoSave: true,
          showGrammarHints: true,
        },
        statistics: {
          totalTimeSpent: 0,
          articlesCompleted: 0,
          sentencesRead: 0,
          lastActiveDate: new Date().toISOString(),
        },
      };

      // 尝试从 localStorage 加载（如果是 web 环境）
      if (typeof window !== 'undefined' && window.localStorage) {
        const saved = localStorage.getItem('dokkai-user-progress');
        if (saved) {
          try {
            return { ...defaultProgress, ...JSON.parse(saved) };
          } catch (e) {
            console.warn('Failed to parse saved user progress:', e);
          }
        }
      }

      return defaultProgress;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.STORAGE_ERROR,
        `Failed to load user progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async saveUserProgress(progress: UserProgress): Promise<void> {
    try {
      // 保存到本地存储
      // 在实际应用中，这里应该发送到后端 API
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('dokkai-user-progress', JSON.stringify(progress));
      }

      console.log('User progress saved:', progress);
    } catch (error) {
      throw this.createError(
        ArticleErrorType.STORAGE_ERROR,
        `Failed to save user progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // 工具方法
  clearCache(): void {
    this.articleCache.clear();
    this.indexCache = null;
    this.categoriesCache = null;
    this.tagsCache = null;
    console.log('API service cache cleared');
  }

  getCachedArticle(id: string): Article | null {
    return this.articleCache.get(id) || null;
  }

  // 获取缓存统计信息
  getCacheStats(): { indexCached: boolean; articlesCached: number; categoriesCached: boolean; tagsCached: boolean } {
    return {
      indexCached: this.indexCache !== null,
      articlesCached: this.articleCache.size,
      categoriesCached: this.categoriesCache !== null,
      tagsCached: this.tagsCache !== null,
    };
  }

  // 检查 API 服务器连接状态
  async checkApiHealth(): Promise<{ healthy: boolean; message: string }> {
    try {
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        return {
          healthy: true,
          message: `API server is healthy. Uptime: ${Math.round(data.uptime)}s`,
        };
      } else {
        return {
          healthy: false,
          message: `API server returned status ${response.status}`,
        };
      }
    } catch (error) {
      return {
        healthy: false,
        message: `Cannot connect to API server: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private createError(type: ArticleErrorType, message: string, details?: any): ArticleError {
    const error = new Error(message) as any;
    error.name = 'ArticleError';
    error.type = type;
    error.details = details;
    error.timestamp = new Date().toISOString();
    return error;
  }
}

// 导出单例实例
export const apiArticleService = new ApiArticleService();

// 导出工具函数
export const getArticleById = (id: string) => apiArticleService.getArticle(id);
export const getAllArticles = () => apiArticleService.getAllArticles();
export const getArticleIndex = () => apiArticleService.getArticleIndex();
export const searchArticles = (query: string, options?: any) => apiArticleService.searchArticles(query, options);
export const getAllCategories = () => apiArticleService.getAllCategories();
export const getAllTags = () => apiArticleService.getAllTags();
export const getArticleLevelData = (articleId: string, level: string) => apiArticleService.getArticleLevelData(articleId, level);
export const updateArticleProgress = (progress: ArticleProgress) =>
  apiArticleService.updateProgress(progress);
export const getUserProgress = () => apiArticleService.getUserProgress();
export const checkApiHealth = () => apiArticleService.checkApiHealth();

// 错误处理工具（重用现有的）
export const isArticleError = (error: any): error is ArticleError => {
  return error && error.name === 'ArticleError';
};

export const getErrorMessage = (error: any): string => {
  if (isArticleError(error)) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unknown error occurred';
};
