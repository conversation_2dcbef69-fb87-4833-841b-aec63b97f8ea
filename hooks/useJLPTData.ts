import { useState, useEffect, useCallback } from 'react';
import { Level, LevelData, JLPTData, LearningProgress } from '../types/jlpt';
import { jlptLevelData, levels, mockJLPTService } from '../data/jlptData';

export const useJLPTData = () => {
  const [data, setData] = useState<JLPTData>(jlptLevelData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await mockJLPTService.getAllLevels();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  }, []);

  const getLevelData = useCallback((level: Level): LevelData | null => {
    return data[level] || null;
  }, [data]);

  return {
    data,
    loading,
    error,
    fetchAllData,
    getLevelData,
    levels
  };
};

export const useJLPTLearning = () => {
  const { data, getLevelData } = useJLPTData();
  const [currentSentence, setCurrentSentence] = useState(0);
  const [progress, setProgress] = useState<LearningProgress>({
    currentSentence: 0,
    totalSentences: 0,
    completedSentences: []
  });

  const maxSentences = data.N5?.sentences.length || 0;

  useEffect(() => {
    setProgress(prev => ({
      ...prev,
      totalSentences: maxSentences,
      currentSentence
    }));
  }, [maxSentences, currentSentence]);

  const nextSentence = useCallback(() => {
    if (currentSentence < maxSentences - 1) {
      setCurrentSentence(prev => {
        const next = prev + 1;
        // 标记当前句子为已完成
        setProgress(prevProgress => ({
          ...prevProgress,
          completedSentences: [...new Set([...prevProgress.completedSentences, prev])]
        }));
        return next;
      });
    }
  }, [currentSentence, maxSentences]);

  const prevSentence = useCallback(() => {
    if (currentSentence > 0) {
      setCurrentSentence(prev => prev - 1);
    }
  }, [currentSentence]);

  const goToSentence = useCallback((index: number) => {
    if (index >= 0 && index < maxSentences) {
      setCurrentSentence(index);
    }
  }, [maxSentences]);

  const getCurrentSentences = useCallback(() => {
    const sentences: Record<Level, string> = {} as Record<Level, string>;
    const grammarFeatures: Record<Level, string> = {} as Record<Level, string>;

    levels.forEach(level => {
      const levelData = getLevelData(level);
      if (levelData) {
        sentences[level] = levelData.sentences[currentSentence] || '';
        grammarFeatures[level] = levelData.grammar_features[currentSentence] || '';
      }
    });

    return { sentences, grammarFeatures };
  }, [currentSentence, getLevelData]);

  const getProgressPercentage = useCallback(() => {
    return maxSentences > 0 ? ((currentSentence + 1) / maxSentences) * 100 : 0;
  }, [currentSentence, maxSentences]);

  const canGoNext = currentSentence < maxSentences - 1;
  const canGoPrev = currentSentence > 0;

  return {
    currentSentence,
    maxSentences,
    progress,
    nextSentence,
    prevSentence,
    goToSentence,
    getCurrentSentences,
    getProgressPercentage,
    canGoNext,
    canGoPrev,
    getLevelData
  };
};