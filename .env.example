# Dokkai 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置
#
# 注意：修改 .env 文件后需要重启 Expo 开发服务器才能生效
# 使用 Ctrl+C 停止服务器，然后重新运行 expo start

# 数据源模式配置
# local: 使用本地JSON文件 (默认)
# api: 使用API服务器
DATA_SOURCE_MODE=local

# API服务器配置
# 只有当 DATA_SOURCE_MODE=api 且 API_BASE_URL 不为空时才会使用API模式
# 留空或注释掉则使用本地模式
API_BASE_URL=

# 开发环境示例配置:
# DATA_SOURCE_MODE=api
# API_BASE_URL=http://localhost:3001/api

# 生产环境示例配置:
# DATA_SOURCE_MODE=api
# API_BASE_URL=https://api.dokkai.com/api
