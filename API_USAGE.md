# Dokkai API 使用指南

## 概述

Dokkai 现在支持两种数据加载模式，通过环境变量配置：

1. **本地模式** (📁): 直接从项目中的 JSON 文件读取数据（默认模式）
2. **API 模式** (🌐): 通过 Mock API 服务器读取数据

## 环境变量配置

### 配置文件设置

1. 复制 `.env.example` 文件为 `.env`：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件配置数据源：
   ```bash
   # 本地模式（默认）
   DATA_SOURCE_MODE=local
   API_BASE_URL=

   # API模式
   DATA_SOURCE_MODE=api
   API_BASE_URL=http://localhost:3001/api
   ```

### 模式选择逻辑

- **默认使用本地模式**
- **只有同时满足以下条件才会使用API模式**：
  - `DATA_SOURCE_MODE=api`
  - `API_BASE_URL` 不为空

### 调试信息

- API调用的URL和模式信息只在console中显示
- 不会在界面上显示切换开关
- 启动时会在console中显示当前配置

## 快速开始

### 1. 启动 API 服务器

```bash
# 进入 API 服务器目录
cd api-server

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm run dev
```

服务器将在 `http://localhost:3001` 启动。

### 2. 使用应用

1. 配置环境变量（如上所述）

2. 启动 Dokkai 应用：
   ```bash
   expo start
   ```

3. 查看console输出确认当前配置：
   ```
   🔧 Data Source Configuration:
      Mode: api
      API URL: http://localhost:3001/api
      Using API Service: true
   ```

## API 模式功能

### 基础功能
- ✅ 文章列表加载
- ✅ 文章详情查看
- ✅ JLPT 级别数据获取
- ✅ 内存缓存优化
- ✅ 错误处理和重试

### 新增功能
- 🆕 文章搜索功能
- 🆕 分类筛选
- 🆕 标签筛选
- 🆕 API 健康状态监控
- 🆕 缓存统计信息

## API 端点

| 端点 | 描述 |
|------|------|
| `GET /api/articles` | 获取文章列表 |
| `GET /api/articles/:id` | 获取文章详情 |
| `GET /api/articles/:id/levels/:level` | 获取特定级别数据 |
| `GET /api/search?q=关键词` | 搜索文章 |
| `GET /api/categories` | 获取分类列表 |
| `GET /api/tags` | 获取标签列表 |
| `GET /api/stats` | 获取统计信息 |

## 调试功能

### API 健康检查
应用会自动检查 API 服务器状态：
- ✅ 绿色指示器：API 正常
- ⚠️ 警告指示器：API 连接异常

### 缓存管理
API 服务器提供缓存管理端点：
- `GET /api/cache-stats` - 查看缓存统计
- `POST /api/cache/clear` - 清空缓存

### 示例请求

```bash
# 获取所有文章
curl http://localhost:3001/api/articles

# 搜索文章
curl "http://localhost:3001/api/search?q=大学生"

# 获取特定级别数据
curl http://localhost:3001/api/articles/daily-life-student/levels/N5

# 筛选文章
curl "http://localhost:3001/api/articles?category=daily-life&difficulty=beginner"
```

## 故障排除

### 常见问题

1. **API 连接失败**
   - 确保 API 服务器在 `http://localhost:3001` 运行
   - 检查防火墙设置
   - 查看服务器启动日志

2. **数据加载缓慢**
   - API 首次请求会较慢（需要读取文件）
   - 后续请求会使用缓存，速度更快

3. **切换模式后数据不一致**
   - 两种模式使用不同的缓存机制
   - 切换模式后数据可能需要重新加载

### 调试步骤

1. 检查 API 服务器状态：
   ```bash
   curl http://localhost:3001/health
   ```

2. 查看服务器日志：
   ```bash
   cd api-server
   npm run dev
   ```

3. 检查应用中的 API 健康指示器

4. 使用浏览器开发者工具查看网络请求

## 开发说明

### 添加新的 API 功能

1. 在 `api-server/src/routes/` 中添加新路由
2. 更新 `apiArticleService.ts` 添加对应方法
3. 在 `useApiArticleData.ts` hook 中暴露新功能
4. 更新组件使用新功能

### 扩展数据模型

1. 更新 `types/api.ts` 中的接口定义
2. 修改 API 服务器的响应格式
3. 更新前端代码适配新的数据结构

## 性能对比

| 功能 | 本地模式 | API 模式 |
|------|----------|----------|
| 初始加载 | 快 | 中等 |
| 缓存效果 | 好 | 更好 |
| 搜索功能 | 无 | 有 |
| 筛选功能 | 基础 | 高级 |
| 扩展性 | 低 | 高 |

## 未来规划

- [ ] 数据持久化到真实数据库
- [ ] 用户认证和权限管理
- [ ] 实时数据同步
- [ ] 文章内容编辑功能
- [ ] 学习进度云端同步

## 反馈

如果遇到问题或有改进建议，请：
1. 查看控制台日志
2. 检查 API 服务器日志
3. 记录重现步骤
4. 提交问题报告
