# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Dokkai is a Japanese learning application built with Expo React Native and TypeScript. It focuses on JLPT (Japanese Language Proficiency Test) learning through comparative study of the same content across different proficiency levels (N5-N1). The app demonstrates the same expressions in varying complexity levels to help learners understand Japanese language progression.

## Development Commands

### Installation & Setup
```bash
npm install
```

### Running the App
```bash
# Start development server
expo start

# Platform-specific builds
expo start --android    # Android emulator
expo start --ios        # iOS simulator
expo start --web        # Web browser
```

### Development Tools
```bash
# Linting
expo lint

# Testing
npm test                # Run Jest tests
# Manual service testing available in __tests__/articleService.test.ts

# Reset project (moves starter code to app-example directory)
npm run reset-project
```

### API Server (Mock/Development)
```bash
# Install API server dependencies
npm run api:install

# Start API development server (runs on http://localhost:3001)
npm run api:dev

# Build API server
npm run api:build

# Test API endpoints
npm run api:test
```

## Architecture & Core Components

### Application Structure
- **Single-page app**: Main functionality in `JLPTLearning` component
- **Stack Navigation**: Uses Expo Router with custom header (`AppHeader`)
- **Dual data sources**: Supports both local JSON files and API server modes
- **Environment-based configuration**: Uses environment variables for data source switching

### Key Components
- `AppHeader`: Main navigation with article selector, menu, and level access
- `JLPTLearning`: Core learning interface with level cards and navigation
- `ArticleSelector`: Modal for switching between articles
- `LevelCard`: Displays content for each JLPT level (N5-N1)
- `NavigationControls`: Sentence navigation (previous/next)
- `FullTextModal`: Complete text view for each level

### Data Architecture
```
data/
├── index.json          # Article metadata and app state
└── articles/           # 36 articles covering diverse topics
    ├── daily-life-student.json
    ├── visa-renewal.json
    ├── rental-contract.json
    ├── job-hunting-process.json
    └── ... (32+ more articles)

api-server/             # Mock API server implementation
├── src/
│   ├── index.ts        # Express server with REST endpoints
│   └── routes/         # API route handlers
└── test-api.js         # API testing script
```

Each article contains:
- Metadata (title, description, category, difficulty, tags)
- JLPT data for all levels (N5-N1) with sentences, grammar features, and full text
- Consistent sentence count across all levels for comparison

### Services & State Management
- **Dual service architecture**:
  - `articleService`: Local JSON file data loading with caching and validation
  - `apiArticleService`: REST API data loading with same interface
  - `dataSourceManager`: Unified service selector based on environment variables
- **Custom hooks**:
  - `useArticleData` / `useApiArticleData`: Article state management for each service
  - `useUnifiedArticleData`: Unified hook that selects between services
  - `useJLPTData`: JLPT level data and navigation logic

### Type System
- Comprehensive TypeScript types in `types/article.ts` and `types/jlpt.ts`
- Runtime validation functions for data integrity
- Strict typing for JLPT levels and article structure

## Key Technologies
- **Framework**: Expo ~53.0.20 with React Native 0.79.5
- **Navigation**: Expo Router ~5.1.4 with file-based routing
- **TypeScript**: Strict mode with `@/*` path mapping
- **React**: Version 19.0.0
- **Testing**: Jest with comprehensive service tests
- **New Architecture**: Enabled (`newArchEnabled: true`)

## Development Patterns

### Data Source Configuration
- **Environment variables**: Use `.env` file to control data source mode
  - `DATA_SOURCE_MODE=local` for JSON file loading (default)
  - `DATA_SOURCE_MODE=api` for REST API mode
  - `API_BASE_URL=http://localhost:3001/api` for API server endpoint
- **Service switching**: Automatic service selection based on environment configuration
  - Default to local mode
  - Only use API mode when both `DATA_SOURCE_MODE=api` AND `API_BASE_URL` is not empty
- **Debug output**: API calls and mode information displayed only in console
- **Unified interface**: Both services implement the same `ArticleService` interface

### Data Loading
- Articles are loaded asynchronously with caching
- Progress is persisted and restored across sessions
- All data includes validation to ensure consistency
- API mode includes health checks and error handling

### Component Communication
- State managed through unified custom hooks
- Progress updates flow through the service layer
- Modal components handle user interactions (article selection, full text view)
- Conditional hook usage allows seamless switching between data sources

### Testing Strategy
- Service layer has comprehensive Jest tests
- API server testing script (`npm run api:test`)
- Manual testing helpers available for development
- Data validation ensures article integrity across all levels

## Environment Configuration

### Setup
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Configure data source in `.env`:
```bash
# Local mode (default)
DATA_SOURCE_MODE=local
API_BASE_URL=

# API mode
DATA_SOURCE_MODE=api
API_BASE_URL=http://localhost:3001/api
```

### Data Source Modes
- **Local mode** (`DATA_SOURCE_MODE=local`): Reads articles from `data/` directory JSON files (default)
- **API mode** (`DATA_SOURCE_MODE=api` + non-empty `API_BASE_URL`): Fetches articles from REST API server

### Development Workflow
1. **Local development**: Use `DATA_SOURCE_MODE=local` for fastest development
2. **API testing**: Start API server with `npm run api:dev`, then switch to `DATA_SOURCE_MODE=api`
3. **Debugging**: Both services provide extensive console logging for troubleshooting
