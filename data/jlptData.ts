import { Level, LevelData, JLPTData } from '../types/jlpt';

export const levelConfig: Record<Level, {color: string; bgColor: string; description: string}> = {
  'N5': {
    color: '#22c55e',
    bgColor: '#f0fdf4',
    description: '入门级：简单句式，基础语法'
  },
  'N4': {
    color: '#3b82f6',
    bgColor: '#eff6ff',
    description: '初级：连接句式，基础复合表达'
  },
  'N3': {
    color: '#a855f7',
    bgColor: '#faf5ff',
    description: '中级：敬语入门，复杂语法结构'
  },
  'N2': {
    color: '#f97316',
    bgColor: '#fff7ed',
    description: '中高级：高级敬语，正式文体'
  },
  'N1': {
    color: '#ef4444',
    bgColor: '#fef2f2',
    description: '高级：文学表达，复杂修辞'
  }
};

export const jlptLevelData: JLPTData = {
  "N5": {
    "sentences": [
      "はじめまして。私の名前は田中です。二十歳です。大学生です。",
      "毎朝七時に起きます。朝ごはんを食べます。大学に行きます。",
      "授業は九時に始まります。",
      "昼休みは友達と学食で昼ごはんを食べます。美味しいです。",
      "午後は図書館で勉強します。宿題があります。大変です。",
      "夕方、家に帰ります。晩ごはんの後、テレビを見ます。",
      "週末は映画を見ます。買い物をします。新しい服を買います。",
      "来月、友達と京都に行きます。楽しみです。",
      "日本語の勉強は難しいです。頑張ります。",
      "ありがとうございました。"
    ],
    "grammar_features": [
      "基本自己介绍", "日常作息", "时间表达", "共同行动+评价", "学习活动+状态", "归家+晚间活动",
      "周末活动", "未来计划+心情", "学习感想+决心", "感谢表达"
    ],
    "full_text": "はじめまして。私の名前は田中です。二十歳です。大学生です。毎朝七時に起きます。朝ごはんを食べます。大学に行きます。授業は九時に始まります。昼休みは友達と学食で昼ごはんを食べます。美味しいです。午後は図書館で勉強します。宿題があります。大変です。夕方、家に帰ります。晩ごはんの後、テレビを見ます。週末は映画を見ます。買い物をします。新しい服を買います。来月、友達と京都に行きます。楽しみです。日本語の勉強は難しいです。頑張ります。ありがとうございました。",
    "grammar_points": ["基本助词(は/が/を/に/で/と)","基础动词ます形","形容词+です","时间表达","简单句结构"],
    "word_count": 95,
    "color": levelConfig.N5.color,
    "bgColor": levelConfig.N5.bgColor,
    "description": levelConfig.N5.description
  },
  "N4": {
    "sentences": [
      "はじめまして。私の名前は田中です。二十歳です。大学生です。",
      "毎朝七時に起きます。朝ごはんを食べてから、大学に行きます。",
      "授業は九時から始まります。",
      "昼休みは友達と一緒に学食で昼ごはんを食べます。とても美味しいです。",
      "午後は図書館で勉強します。宿題がたくさんあります。ちょっと大変です。",
      "夕方、家に帰ります。晩ごはんの後、テレビを見ます。",
      "週末は映画を見たり、買い物をしたりします。新しい服を買うのが好きです。",
      "来月、友達と京都に旅行に行きます。とても楽しみです。",
      "日本語の勉強は難しいですが、頑張ります。",
      "ありがとうございました。"
    ],
    "grammar_features": [
      "基本自己介绍", "て形连接", "から表示起点", "一緒に+程度副词", "量词+程度副词", "归家+晚间活动",
      "たり形并列+の+が好き", "旅行+程度副词", "逆接が", "感谢表达"
    ],
    "full_text": "はじめまして。私の名前は田中です。二十歳です。大学生です。毎朝七時に起きます。朝ごはんを食べてから、大学に行きます。授業は九時から始まります。昼休みは友達と一緒に学食で昼ごはんを食べます。とても美味しいです。午後は図書館で勉強します。宿題がたくさんあります。ちょっと大変です。夕方、家に帰ります。晩ごはんの後、テレビを見ます。週末は映画を見たり、買い物をしたりします。新しい服を買うのが好きです。来月、友達と京都に旅行に行きます。とても楽しみです。日本語の勉強は難しいですが、頑張ります。ありがとうございました。",
    "grammar_points": ["て形连接(てから)","たり形并列","动词修饰(Vのが好き)","逆接条件(ですが)","副词修饰","一緒に"],
    "word_count": 108,
    "color": levelConfig.N4.color,
    "bgColor": levelConfig.N4.bgColor,
    "description": levelConfig.N4.description
  },
  "N3": {
    "sentences": [
      "初めてお会いします。私の名前は田中と申します。二十歳で、大学生をしております。",
      "毎朝七時に起床してから、朝食を摂って大学に通学します。",
      "授業は九時から開始されます。",
      "昼休みには友人と一緒に学生食堂で昼食を取ります。非常に美味しいです。",
      "午後は図書館で学習に励みます。課題が多くあり、少し困難です。",
      "夕方になると帰宅します。夕食後、テレビを視聴します。",
      "週末には映画鑑賞や買い物などをします。新しい洋服を購入することを好んでいます。",
      "来月、友人と京都へ旅行に参ります。大変楽しみにしております。",
      "日本語の学習は困難ですが、努力を続けています。",
      "ありがとうございました。"
    ],
    "grammar_features": [
      "謙譲語+ております", "複合動詞", "受身形", "正式表現+非常に", "に励む+複合述語", "になると+漢語動詞",
      "など並列+ことを好む", "謙譲語参る+ております", "継続ている", "感謝表達"
    ],
    "full_text": "初めてお会いします。私の名前は田中と申します。二十歳で、大学生をしております。毎朝七時に起床してから、朝食を摂って大学に通学します。授業は九時から開始されます。昼休みには友人と一緒に学生食堂で昼食を取ります。非常に美味しいです。午後は図書館で学習に励みます。課題が多くあり、少し困難です。夕方になると帰宅します。夕食後、テレビを視聴します。週末には映画鑑賞や買い物などをします。新しい洋服を購入することを好んでいます。来月、友人と京都へ旅行に参ります。大変楽しみにしております。日本語の学習は困難ですが、努力を続けています。ありがとうございました。",
    "grammar_points": ["敬语表达(申します/しております/参ります)","被动形(開始されます)","に励む","ことを好む","続けています","になると"],
    "word_count": 125,
    "color": levelConfig.N3.color,
    "bgColor": levelConfig.N3.bgColor,
    "description": levelConfig.N3.description
  },
  "N2": {
    "sentences": [
      "初めてお目にかかります。田中と申します。二十歳の大学生でございます。",
      "毎朝七時に起床いたしまして、朝食を済ませてから大学へ向かいます。",
      "授業は九時より開始いたします。",
      "昼休みには友人たちと学生食堂で昼食を共にいたします。大変美味でございます。",
      "午後は図書館において学習に取り組んでおります。課題が山積しており、やや困難を感じております。",
      "夕刻になりましたら帰宅いたします。夕食を終えた後、テレビを拝見いたします。",
      "週末においては映画鑑賞や買い物等に時間を費やしております。新しい服装を購入することに喜びを感じております。",
      "来月、友人と共に京都への旅行を予定しております。心より楽しみにしております。",
      "日本語の習得は困難を伴いますが、継続的に努力を重ねております。",
      "心より感謝申し上げます。"
    ],
    "grammar_features": [
      "尊敬語+でございます", "いたしまして+へ向かう", "より敬語", "を共にする+でございます", "において+を伴う", "ましたら+拝見いたす",
      "においては+ことに感じる", "と共に+心より", "を伴う+を重ねる", "申し上げる"
    ],
    "full_text": "初めてお目にかかります。田中と申します。二十歳の大学生でございます。毎朝七時に起床いたしまして、朝食を済ませてから大学へ向かいます。授業は九時より開始いたします。昼休みには友人たちと学生食堂で昼食を共にいたします。大変美味でございます。午後は図書館において学習に取り組んでおります。課題が山積しており、やや困難を感じております。夕刻になりましたら帰宅いたします。夕食を終えた後、テレビを拝見いたします。週末においては映画鑑賞や買い物等に時間を費やしております。新しい服装を購入することに喜びを感じております。来月、友人と共に京都への旅行を予定しております。心より楽しみにしております。日本語の習得は困難を伴いますが、継続的に努力を重ねております。心より感謝申し上げます。",
    "grammar_points": ["高级敬语(いたします/ございます/申し上げます)","において/に対して","を共にする","困難を伴う","努力を重ねる","予定しております"],
    "word_count": 142,
    "color": levelConfig.N2.color,
    "bgColor": levelConfig.N2.bgColor,
    "description": levelConfig.N2.description
  },
  "N1": {
    "sentences": [
      "初めてお目にかかります。田中と申す者でございます。二十歳になる大学生でございます。",
      "毎朝七時に起床し、朝食を摂った後、大学へと足を向けます。",
      "講義は九時より開講されます。",
      "昼の休憩時間には友人らと学食にて昼食を共にし、その味わいに舌鼓を打っております。",
      "午後は図書館に籠もり、学習に専念しております。課題が山のように積まれており、なかなか手強い状況でございます。",
      "日が傾く頃には帰路につきます。夕餉を済ませた後は、テレビに目を通しております。",
      "週末ともなれば、映画を観たり街を練り歩いたりと、思い思いに過ごしております。新調した衣服を身に纏うことに、ささやかな喜びを覚えております。",
      "来る月には友人と連れ立って、古都京都への小旅行を企てております。今から胸を躍らせているところでございます。",
      "日本語の習得には骨が折れますが、地道に励んでいる次第です。",
      "ご清聴いただき、誠にありがとうございました。"
    ],
    "grammar_features": [
      "申す者+になる", "足を向ける", "開講される", "舌鼓を打つ", "に籠もる+手強い", "日が傾く+目を通す",
      "ともなれば+身に纏う", "連れ立って+胸を躍らせる", "骨が折れる", "ご清聴"
    ],
    "full_text": "初めてお目にかかります。田中と申す者でございます。二十歳になる大学生でございます。毎朝七時に起床し、朝食を摂った後、大学へと足を向けます。講義は九時より開講されます。昼の休憩時間には友人らと学食にて昼食を共にし、その味わいに舌鼓を打っております。午後は図書館に籠もり、学習に専念しております。課題が山のように積まれており、なかなか手強い状況でございます。日が傾く頃には帰路につきます。夕餉を済ませた後は、テレビに目を通しております。週末ともなれば、映画を観たり街を練り歩いたりと、思い思いに過ごしております。新調した衣服を身に纏うことに、ささやかな喜びを覚えております。来る月には友人と連れ立って、古都京都への小旅行を企てております。今から胸を躍らせているところでございます。日本語の習得には骨が折れますが、地道に励んでいる次第です。ご清聴いただき、誠にありがとうございました。",
    "grammar_points": ["古典的表現","慣用句(足を向ける/舌鼓を打つ/籠もる/骨が折れる)","文学的修辞","高度な敬語","複雑な修飾構造","ところでございます"],
    "word_count": 158,
    "color": levelConfig.N1.color,
    "bgColor": levelConfig.N1.bgColor,
    "description": levelConfig.N1.description
  }
};

export const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];

// 为未来API集成准备的数据获取接口
export interface JLPTDataService {
  getLevelData(level: Level): Promise<LevelData>;
  getAllLevels(): Promise<JLPTData>;
  updateProgress(progress: any): Promise<void>;
}

// 模拟API调用的数据服务
export const mockJLPTService: JLPTDataService = {
  async getLevelData(level: Level): Promise<LevelData> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    return jlptLevelData[level];
  },
  
  async getAllLevels(): Promise<JLPTData> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return jlptLevelData;
  },
  
  async updateProgress(progress: any): Promise<void> {
    // 未来可以实现到后端的进度保存
    console.log('Progress saved:', progress);
  }
};