import { JLPTData, Level } from './jlpt';

export interface Article {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number; // in minutes
  jlptData: JLPTData;
}

export interface ArticleMetadata {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  sentenceCount: number;
}

export interface ArticleIndex {
  version: string;
  lastUpdated: string;
  currentArticle: string;
  articles: ArticleMetadata[];
}

export interface ArticleProgress {
  articleId: string;
  currentSentence: number;
  completedSentences: number[];
  totalSentences: number;
  lastAccessTime: string;
  timeSpent: number; // in seconds
}

export interface UserProgress {
  currentArticleId: string;
  articleProgress: Record<string, ArticleProgress>;
  preferences: {
    preferredLevels: Level[];
    autoSave: boolean;
    showGrammarHints: boolean;
  };
  statistics: {
    totalTimeSpent: number;
    articlesCompleted: number;
    sentencesRead: number;
    lastActiveDate: string;
  };
}

// Service interfaces for data management
export interface ArticleService {
  getArticleIndex(): Promise<ArticleIndex>;
  getArticle(id: string): Promise<Article>;
  getAllArticles(): Promise<Article[]>;
  getFilteredArticles(options?: {
    tags?: string[];
    categories?: string[];
    difficulties?: string[];
  }): Promise<ArticleMetadata[]>;
  getAllTags(): Promise<string[]>;
  updateProgress(progress: ArticleProgress): Promise<void>;
  getUserProgress(): Promise<UserProgress>;
  saveUserProgress(progress: UserProgress): Promise<void>;
}

// Component prop interfaces
export interface ArticleSelectorProps {
  visible: boolean;
  articles: ArticleMetadata[];
  currentArticleId: string;
  onSelect: (articleId: string) => void;
  onClose: () => void;
  loading?: boolean;
}

export interface AppHeaderProps {
  currentArticle: Article | null;
  onMenuPress: () => void;
  onLevelSelect: (level: Level) => void;
  availableLevels: Level[];
  loading?: boolean;
}

export interface LevelSelectorProps {
  visible: boolean;
  levels: Level[];
  currentLevel: Level | null;
  onSelect: (level: Level) => void;
  onClose: () => void;
}

// Error types for better error handling
export enum ArticleErrorType {
  NETWORK_ERROR = 'network_error',
  DATA_PARSE_ERROR = 'data_parse_error',
  FILE_NOT_FOUND = 'file_not_found',
  INVALID_DATA = 'invalid_data',
  STORAGE_ERROR = 'storage_error'
}

export interface ArticleError {
  type: ArticleErrorType;
  message: string;
  details?: any;
  timestamp: string;
}

// Validation functions
export const validateArticle = (data: any): data is Article => {
  return (
    typeof data.id === 'string' &&
    typeof data.title === 'string' &&
    typeof data.description === 'string' &&
    typeof data.createdAt === 'string' &&
    Array.isArray(data.tags) &&
    typeof data.category === 'string' &&
    ['beginner', 'intermediate', 'advanced'].includes(data.difficulty) &&
    typeof data.estimatedReadingTime === 'number' &&
    data.jlptData &&
    validateJLPTData(data.jlptData)
  );
};

export const validateArticleIndex = (data: any): data is ArticleIndex => {
  return (
    typeof data.version === 'string' &&
    typeof data.lastUpdated === 'string' &&
    typeof data.currentArticle === 'string' &&
    Array.isArray(data.articles) &&
    data.articles.every((article: any) => validateArticleMetadata(article))
  );
};

export const validateArticleMetadata = (data: any): data is ArticleMetadata => {
  return (
    typeof data.id === 'string' &&
    typeof data.title === 'string' &&
    typeof data.description === 'string' &&
    typeof data.createdAt === 'string' &&
    Array.isArray(data.tags) &&
    typeof data.category === 'string' &&
    ['beginner', 'intermediate', 'advanced'].includes(data.difficulty) &&
    typeof data.estimatedReadingTime === 'number' &&
    typeof data.sentenceCount === 'number'
  );
};

// Helper function to validate JLPT data (imported from jlpt.ts)
const validateJLPTData = (data: any): boolean => {
  const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];

  return levels.every(level => {
    const levelData = data[level];
    return (
      levelData &&
      Array.isArray(levelData.sentences) &&
      Array.isArray(levelData.grammar_features) &&
      typeof levelData.full_text === 'string' &&
      Array.isArray(levelData.grammar_points) &&
      typeof levelData.word_count === 'number' &&
      typeof levelData.color === 'string' &&
      typeof levelData.bgColor === 'string' &&
      typeof levelData.description === 'string'
    );
  });
};

// Utility functions
export const createArticleMetadata = (article: Article): ArticleMetadata => {
  return {
    id: article.id,
    title: article.title,
    description: article.description,
    author: article.author,
    createdAt: article.createdAt,
    updatedAt: article.updatedAt,
    tags: article.tags,
    category: article.category,
    difficulty: article.difficulty,
    estimatedReadingTime: article.estimatedReadingTime,
    sentenceCount: article.jlptData.N5?.sentences.length || 0
  };
};

export const calculateReadingTime = (jlptData: JLPTData): number => {
  // Estimate reading time based on sentence count and complexity
  const sentenceCount = jlptData.N5?.sentences.length || 0;
  const baseTime = sentenceCount * 0.5; // 30 seconds per sentence
  return Math.ceil(baseTime / 60); // Convert to minutes
};

export const generateArticleId = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .substring(0, 50);
};
